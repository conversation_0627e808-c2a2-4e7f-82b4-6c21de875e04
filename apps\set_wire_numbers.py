#!/usr/bin/env python3
"""
E3 Wire Number Assignment Script

This script reads through the current open E3 project and for every connection,
calculates wire numbers based on page number and grid/ladder position, then
sets the "Wire number" attribute on the corresponding net segments.
The wire number format is: f"{page_number}({grid_position})"
The end that results in the lowest wire number is used.
All net segments with the same signal get the same wire number.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import logging
import sys
from collections import defaultdict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wire_numbering.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class WireNumberAssigner:
    def __init__(self):
        self.app = None
        self.job = None
        self.connection = None
        self.pin = None
        self.sheet = None
        self.signal = None
        self.net = None
        self.net_segment = None
        
    def connect_to_e3(self):
        """Connect to the open E3 application"""
        try:
            self.app = win32com.client.GetActiveObject("CT.Application")
            self.job = self.app.CreateJobObject()
            self.connection = self.job.CreateConnectionObject()
            self.pin = self.job.CreatePinObject()
            self.sheet = self.job.CreateSheetObject()
            self.signal = self.job.CreateSignalObject()
            self.net = self.job.CreateNetObject()
            self.net_segment = self.job.CreateNetSegmentObject()
            logging.info("Successfully connected to E3 application")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to E3: {e}")
            return False
    
    def get_pin_location_info(self, pin_id):
        """Get location information for a pin"""
        try:
            self.pin.SetId(pin_id)

            # Get schema location - E3 API returns (sheet_id, x, y, grid_desc, column, row)
            try:
                result = self.pin.GetSchemaLocation()

                if isinstance(result, tuple) and len(result) >= 6:
                    sheet_id = result[0]
                    _ = result[1]  # x coordinate (not used)
                    _ = result[2]  # y coordinate (not used)
                    grid_desc = result[3]
                    column = result[4]
                    row = result[5]
                else:
                    logging.warning(f"Unexpected GetSchemaLocation result format for pin {pin_id}: {result}")
                    return None, None, None

            except Exception as e:
                logging.error(f"Error calling GetSchemaLocation for pin {pin_id}: {e}")
                return None, None, None

            if not sheet_id or sheet_id <= 0:
                logging.debug(f"Pin {pin_id} has no valid schema location (sheet_id: {sheet_id})")
                return None, None, None


            try:
                self.sheet.SetId(sheet_id)
                page_number = self.sheet.GetName()
            except Exception as e:
                logging.error(f"Error getting sheet assignment for sheet {sheet_id}: {e}")
                page_number = "UNKNOWN"

            # Extract grid position from grid_desc or use column/row
            grid_position = self.extract_grid_position(grid_desc, column, row)

            logging.debug(f"Pin {pin_id}: Sheet {sheet_id}, Page {page_number}, Grid {grid_position}")

            return page_number, grid_position, sheet_id

        except Exception as e:
            logging.error(f"Error getting pin location for pin {pin_id}: {e}")
            return None, None, None
    
    def extract_grid_position(self, grid_desc, column, row):
        """Extract grid position from grid description or column/row"""
        try:
            # If we have grid_desc in format "/sheet.grid", extract the grid part
            if grid_desc and "." in grid_desc:
                grid_part = grid_desc.split(".")[-1]
                return grid_part
            
            # If we have column and row, combine them
            if column and row:
                return f"{column}{row}"
            
            # Fallback to just column or row if available
            if column:
                return column
            if row:
                return row
                
            return "UNKNOWN"
            
        except Exception as e:
            logging.error(f"Error extracting grid position: {e}")
            return "UNKNOWN"
    
    def calculate_wire_number(self, page_number, grid_position):
        """Calculate wire number from page and grid position"""
        try:
            # Handle empty or None page numbers
            if not page_number or page_number.strip() == "":
                page_num = "0"
            else:
                page_num = str(page_number).strip()
            
            # Format: page_number(grid_position)
            wire_number = f"{page_num}({grid_position})"
            return wire_number
            
        except Exception as e:
            logging.error(f"Error calculating wire number: {e}")
            return "ERROR"
    
    def get_connection_wire_numbers(self, connection_id):
        """Get potential wire numbers for both ends of a connection"""
        try:
            self.connection.SetId(connection_id)

            # Get pin IDs for this connection
            pin_ids_result = self.connection.GetPinIds()
            if not pin_ids_result:
                logging.warning(f"Connection {connection_id} has no pins")
                return []

            wire_numbers = []

            # E3 API returns (count, tuple_of_ids) for pin IDs too
            actual_pin_ids = []
            if isinstance(pin_ids_result, tuple) and len(pin_ids_result) >= 2:
                _ = pin_ids_result[0]  # count (not used)
                pin_ids = pin_ids_result[1]

                if isinstance(pin_ids, tuple):
                    # Filter out None values and invalid pin IDs (like 0)
                    actual_pin_ids = [pid for pid in pin_ids if pid is not None and pid != 0]
                else:
                    if pin_ids is not None and pin_ids != 0:
                        actual_pin_ids = [pin_ids]
            else:
                logging.warning(f"Unexpected pin IDs format for connection {connection_id}: {type(pin_ids_result)}")
                return []

            logging.debug(f"Connection {connection_id} has {len(actual_pin_ids)} valid pins")

            for pin_id in actual_pin_ids:
                page_number, grid_position, _ = self.get_pin_location_info(pin_id)
                if page_number is not None and grid_position is not None:
                    wire_number = self.calculate_wire_number(page_number, grid_position)
                    wire_numbers.append(wire_number)
                    logging.debug(f"Pin {pin_id}: Page {page_number}, Grid {grid_position} -> Wire {wire_number}")

            return wire_numbers

        except Exception as e:
            logging.error(f"Error getting wire numbers for connection {connection_id}: {e}")
            return []
    
    def get_lowest_wire_number(self, wire_numbers):
        """Get the lowest wire number from a list"""
        if not wire_numbers:
            return None
        
        # Sort wire numbers to get the lowest one
        # This will sort lexicographically, which should work for most cases
        sorted_numbers = sorted(wire_numbers)
        return sorted_numbers[0]
    

    def get_net_segments_for_connection(self, connection_id):
        """Get all net segment IDs for a given connection"""
        try:
            self.connection.SetId(connection_id)
            net_segment_ids_result = self.connection.GetNetSegmentIds()

            if not net_segment_ids_result:
                return []

            actual_net_segments = []
            if isinstance(net_segment_ids_result, tuple) and len(net_segment_ids_result) >= 2:
                _ = net_segment_ids_result[0]  # count (not used)
                net_segment_ids = net_segment_ids_result[1]

                if isinstance(net_segment_ids, tuple):
                    actual_net_segments = [nsid for nsid in net_segment_ids if nsid is not None and nsid != 0]
                else:
                    if net_segment_ids is not None and net_segment_ids != 0:
                        actual_net_segments = [net_segment_ids]
            else:
                logging.warning(f"Unexpected net segment IDs format for connection {connection_id}: {type(net_segment_ids_result)}")

            return actual_net_segments

        except Exception as e:
            logging.error(f"Error getting net segments for connection {connection_id}: {e}")
            return []

    def process_connections(self):
        """Process all connections in the project"""
        try:
            # Get all connection IDs
            connection_ids_result = self.job.GetAllConnectionIds()
            if not connection_ids_result:
                logging.warning("No connections found in project")
                return

            # E3 API returns (count, tuple_of_ids)
            actual_connections = []
            if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                count = connection_ids_result[0]
                connection_ids = connection_ids_result[1]

                logging.info(f"E3 reports {count} connections")

                if isinstance(connection_ids, tuple):
                    # Filter out None values
                    actual_connections = [cid for cid in connection_ids if cid is not None]
                else:
                    actual_connections = [connection_ids] if connection_ids is not None else []
            else:
                logging.warning(f"Unexpected connection IDs format: {type(connection_ids_result)}")
                return

            logging.info(f"Found {len(actual_connections)} valid connections to process")

            # Group net segments by signal name
            signal_groups = defaultdict(list)
            net_segment_wire_numbers = {}

            # First pass: calculate wire numbers for each connection and its net segments
            for conn_id in actual_connections:
                if conn_id is None:
                    continue

                try:
                    self.connection.SetId(conn_id)
                    signal_name = self.connection.GetSignalName()

                    # Get potential wire numbers for this connection
                    wire_numbers = self.get_connection_wire_numbers(conn_id)
                    lowest_wire_number = self.get_lowest_wire_number(wire_numbers)

                    if lowest_wire_number:
                        # Get all net segments for this connection
                        net_segment_ids = self.get_net_segments_for_connection(conn_id)

                        for net_segment_id in net_segment_ids:
                            net_segment_wire_numbers[net_segment_id] = lowest_wire_number
                            signal_groups[signal_name].append((net_segment_id, lowest_wire_number))
                            logging.debug(f"NetSegment {net_segment_id} (Signal: {signal_name}) -> Wire: {lowest_wire_number}")
                    else:
                        logging.warning(f"Could not calculate wire number for connection {conn_id}")

                except Exception as e:
                    logging.error(f"Error processing connection {conn_id}: {e}")

            # Second pass: determine the lowest wire number for each signal group
            signal_wire_numbers = {}
            for signal_name, net_segments in signal_groups.items():
                if net_segments:
                    # Get all wire numbers for this signal
                    wire_numbers = [wire_num for _, wire_num in net_segments]
                    lowest_wire_number = self.get_lowest_wire_number(wire_numbers)
                    signal_wire_numbers[signal_name] = lowest_wire_number
                    logging.info(f"Signal '{signal_name}' assigned wire number: {lowest_wire_number}")

            # Third pass: set wire numbers for all net segments in each signal group
            updated_count = 0
            for signal_name, net_segments in signal_groups.items():
                wire_number = signal_wire_numbers.get(signal_name)
                if wire_number:
                    for net_segment_id, _ in net_segments:
                        try:
                            self.net_segment.SetId(net_segment_id)
                            self.net_segment.SetAttributeValue("Wire number", wire_number)
                            updated_count += 1
                            logging.debug(f"Set wire number '{wire_number}' for net segment {net_segment_id}")

                        except Exception as e:
                            logging.error(f"Error setting wire number for net segment {net_segment_id}: {e}")

            logging.info(f"Successfully updated wire numbers for {updated_count} net segments")

        except Exception as e:
            logging.error(f"Error processing connections: {e}")
    
    def run(self):
        """Main execution method"""
        logging.info("Starting wire number assignment process")
        
        if not self.connect_to_e3():
            logging.error("Failed to connect to E3. Make sure E3 is running with a project open.")
            return False
        
        try:
            self.process_connections()
            logging.info("Wire number assignment completed successfully")
            return True
            
        except Exception as e:
            logging.error(f"Error during wire number assignment: {e}")
            return False
        
        finally:
            # Clean up COM objects
            self.app = None
            self.job = None
            self.connection = None
            self.pin = None
            self.sheet = None
            self.signal = None
            self.net = None
            self.net_segment = None

def main():
    """Main function"""
    assigner = WireNumberAssigner()
    success = assigner.run()
    
    if success:
        print("Wire number assignment completed successfully!")
        print("Check the log file 'wire_numbering.log' for details.")
    else:
        print("Wire number assignment failed. Check the log file for errors.")
        sys.exit(1)

if __name__ == "__main__":
    main()
