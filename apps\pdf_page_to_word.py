#!/usr/bin/env python3
"""
PDF Page to Word Converter

This application allows you to:
1. Select a PDF file
2. Browse through the PDF pages with preview
3. Select a specific page
4. Export that page to a Word document

Dependencies:
- customtkinter for GUI
- PyMuPDF (fitz) for PDF processing and preview
- python-docx for Word document creation
- Pillow for image processing
"""

import os
import sys
import logging
import tempfile
import subprocess
import tkinter as tk
from tkinter import filedialog, messagebox
from io import BytesIO
import customtkinter as ctk
from PIL import Image, ImageTk
import fitz  # PyMuPDF
from docx import Document
from docx.shared import Inches

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils import setup_logging
    from lib.theme_utils import apply_theme
except ImportError:
    def setup_logging(log_name="pdf_page_to_word"):
        """Basic logging setup"""
        logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
        return "pdf_page_to_word.log"
    
    def apply_theme(theme_name, mode):
        """Basic theme application"""
        ctk.set_appearance_mode(mode)


class PDFPageToWordApp(ctk.CTk):
    """Main application for converting PDF pages to Word documents."""
    
    def __init__(self):
        """Initialize the application."""
        super().__init__()
        
        # Set up the window
        self.title("PDF Page to Word Converter")
        self.geometry("1400x900")
        self.minsize(1200, 800)
        
        # Apply theme
        try:
            apply_theme("red", "dark")
        except:
            ctk.set_appearance_mode("dark")
        
        # Initialize variables
        self.pdf_document = None
        self.current_page = 0
        self.total_pages = 0
        self.pdf_path = ""
        self.preview_image = None
        
        # Create widgets
        self.create_widgets()
        
        # Set up logging
        setup_logging("pdf_page_to_word")
        logging.info("PDF Page to Word Converter started")
    
    def create_widgets(self):
        """Create the application widgets."""
        # Configure grid layout
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(1, weight=1)
        
        # File selection frame
        self.file_frame = ctk.CTkFrame(self)
        self.file_frame.grid(row=0, column=0, columnspan=2, padx=20, pady=10, sticky="ew")
        
        self.btn_select_pdf = ctk.CTkButton(
            self.file_frame,
            text="Select PDF File",
            command=self.select_pdf_file,
            width=150,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#C53F3F",
            hover_color="#A02222"
        )
        self.btn_select_pdf.pack(side="left", padx=10, pady=10)
        
        self.lbl_pdf_path = ctk.CTkLabel(
            self.file_frame,
            text="No PDF file selected",
            anchor="w",
            font=("Arial", 12)
        )
        self.lbl_pdf_path.pack(side="left", fill="x", expand=True, padx=10, pady=10)
        
        # Control panel frame
        self.control_frame = ctk.CTkFrame(self)
        self.control_frame.grid(row=1, column=0, padx=(20, 10), pady=10, sticky="nsew")
        self.control_frame.grid_rowconfigure(2, weight=1)
        
        # Page navigation
        self.nav_frame = ctk.CTkFrame(self.control_frame)
        self.nav_frame.pack(fill="x", padx=10, pady=10)
        
        self.btn_prev = ctk.CTkButton(
            self.nav_frame,
            text="◀ Previous",
            command=self.prev_page,
            width=100,
            state="disabled"
        )
        self.btn_prev.pack(side="left", padx=5)
        
        self.lbl_page_info = ctk.CTkLabel(
            self.nav_frame,
            text="Page 0 of 0",
            font=("Arial", 14, "bold")
        )
        self.lbl_page_info.pack(side="left", expand=True, padx=10)
        
        self.btn_next = ctk.CTkButton(
            self.nav_frame,
            text="Next ▶",
            command=self.next_page,
            width=100,
            state="disabled"
        )
        self.btn_next.pack(side="right", padx=5)
        
        # Page selection
        self.page_frame = ctk.CTkFrame(self.control_frame)
        self.page_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(self.page_frame, text="Go to page:", font=("Arial", 12)).pack(side="left", padx=5)
        
        self.entry_page = ctk.CTkEntry(self.page_frame, width=80)
        self.entry_page.pack(side="left", padx=5)
        self.entry_page.bind("<Return>", self.go_to_page)
        
        self.btn_go = ctk.CTkButton(
            self.page_frame,
            text="Go",
            command=self.go_to_page,
            width=60
        )
        self.btn_go.pack(side="left", padx=5)
        
        # Export button
        self.btn_export = ctk.CTkButton(
            self.control_frame,
            text="Export Current Page to Word",
            command=self.export_to_word,
            height=50,
            font=("Arial", 16, "bold"),
            fg_color="#2AA876",
            hover_color="#22815D",
            state="disabled"
        )
        self.btn_export.pack(fill="x", padx=10, pady=20)
        
        # Status label
        self.lbl_status = ctk.CTkLabel(
            self.control_frame,
            text="Select a PDF file to begin",
            font=("Arial", 12),
            text_color="#888888"
        )
        self.lbl_status.pack(fill="x", padx=10, pady=10)
        
        # Preview frame
        self.preview_frame = ctk.CTkFrame(self)
        self.preview_frame.grid(row=1, column=1, padx=(10, 20), pady=10, sticky="nsew")
        
        # Preview canvas with scrollbars
        self.canvas_frame = ctk.CTkFrame(self.preview_frame)
        self.canvas_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.canvas = tk.Canvas(self.canvas_frame, bg="#2b2b2b", highlightthickness=0)
        self.v_scrollbar = tk.Scrollbar(self.canvas_frame, orient="vertical", command=self.canvas.yview)
        self.h_scrollbar = tk.Scrollbar(self.canvas_frame, orient="horizontal", command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=self.v_scrollbar.set, xscrollcommand=self.h_scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.v_scrollbar.pack(side="right", fill="y")
        self.h_scrollbar.pack(side="bottom", fill="x")
        
        # Preview label for when no PDF is loaded
        self.lbl_preview = ctk.CTkLabel(
            self.preview_frame,
            text="PDF preview will appear here",
            font=("Arial", 16),
            text_color="#666666"
        )
        self.lbl_preview.pack(expand=True)
    
    def select_pdf_file(self):
        """Open file dialog to select a PDF file."""
        file_path = filedialog.askopenfilename(
            title="Select PDF File",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if file_path:
            self.load_pdf(file_path)
    
    def load_pdf(self, file_path):
        """Load a PDF file and initialize the preview."""
        try:
            self.lbl_status.configure(text="Loading PDF...")
            self.update()
            
            # Close previous document if any
            if self.pdf_document:
                self.pdf_document.close()
            
            # Open the new PDF
            self.pdf_document = fitz.open(file_path)
            self.pdf_path = file_path
            self.total_pages = len(self.pdf_document)
            self.current_page = 0
            
            # Update UI
            self.lbl_pdf_path.configure(text=os.path.basename(file_path))
            self.lbl_page_info.configure(text=f"Page {self.current_page + 1} of {self.total_pages}")
            
            # Enable controls
            self.btn_next.configure(state="normal" if self.total_pages > 1 else "disabled")
            self.btn_prev.configure(state="disabled")
            self.btn_export.configure(state="normal")
            self.entry_page.configure(state="normal")
            self.btn_go.configure(state="normal")
            
            # Hide the preview label and show the canvas
            self.lbl_preview.pack_forget()
            self.canvas_frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # Load the first page
            self.load_page_preview()
            
            self.lbl_status.configure(text=f"PDF loaded successfully - {self.total_pages} pages")
            logging.info(f"PDF loaded: {file_path} ({self.total_pages} pages)")
            
        except Exception as e:
            error_msg = f"Error loading PDF: {str(e)}"
            self.lbl_status.configure(text=error_msg)
            messagebox.showerror("Error", error_msg)
            logging.error(f"Error loading PDF {file_path}: {e}")
    
    def load_page_preview(self):
        """Load and display the current page preview."""
        if not self.pdf_document:
            return
        
        try:
            # Get the current page
            page = self.pdf_document[self.current_page]
            
            # Render page to image
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("ppm")
            
            # Convert to PIL Image
            pil_image = Image.open(BytesIO(img_data))
            
            # Convert to PhotoImage for tkinter
            self.preview_image = ImageTk.PhotoImage(pil_image)
            
            # Clear canvas and add image
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor="nw", image=self.preview_image)
            
            # Update scroll region
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
        except Exception as e:
            error_msg = f"Error loading page preview: {str(e)}"
            self.lbl_status.configure(text=error_msg)
            logging.error(f"Error loading page preview: {e}")

    def prev_page(self):
        """Navigate to the previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self.update_page_display()

    def next_page(self):
        """Navigate to the next page."""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.update_page_display()

    def go_to_page(self, event=None):
        """Navigate to a specific page number."""
        try:
            page_num = int(self.entry_page.get())
            if 1 <= page_num <= self.total_pages:
                self.current_page = page_num - 1
                self.update_page_display()
            else:
                messagebox.showwarning("Invalid Page", f"Please enter a page number between 1 and {self.total_pages}")
        except ValueError:
            messagebox.showwarning("Invalid Input", "Please enter a valid page number")

    def update_page_display(self):
        """Update the page display and navigation controls."""
        if not self.pdf_document:
            return

        # Update page info
        self.lbl_page_info.configure(text=f"Page {self.current_page + 1} of {self.total_pages}")

        # Update navigation buttons
        self.btn_prev.configure(state="normal" if self.current_page > 0 else "disabled")
        self.btn_next.configure(state="normal" if self.current_page < self.total_pages - 1 else "disabled")

        # Clear page entry
        self.entry_page.delete(0, tk.END)

        # Load page preview
        self.load_page_preview()

    def export_to_word(self):
        """Export the current page to a Word document."""
        if not self.pdf_document:
            messagebox.showwarning("No PDF", "Please select a PDF file first")
            return

        try:
            # Ask user where to save the Word document
            output_path = filedialog.asksaveasfilename(
                title="Save Word Document",
                defaultextension=".docx",
                filetypes=[("Word documents", "*.docx"), ("All files", "*.*")],
                initialname=f"{os.path.splitext(os.path.basename(self.pdf_path))[0]}_page_{self.current_page + 1}.docx"
            )

            if not output_path:
                return

            self.lbl_status.configure(text="Exporting page to Word...")
            self.update()

            # Get the current page
            page = self.pdf_document[self.current_page]

            # Render page to high-quality image
            mat = fitz.Matrix(3.0, 3.0)  # 3x zoom for high quality
            pix = page.get_pixmap(matrix=mat)

            # Save to temporary image file
            temp_image_path = tempfile.mktemp(suffix='.png')
            pix.save(temp_image_path)

            # Create Word document
            doc = Document()

            # Add title
            title = doc.add_heading(f'PDF Page Export', 0)
            title.alignment = 1  # Center alignment

            # Add source information
            info_para = doc.add_paragraph()
            info_para.add_run(f"Source: ").bold = True
            info_para.add_run(f"{os.path.basename(self.pdf_path)}\n")
            info_para.add_run(f"Page: ").bold = True
            info_para.add_run(f"{self.current_page + 1} of {self.total_pages}\n")
            info_para.add_run(f"Exported: ").bold = True
            info_para.add_run(f"{self.get_current_timestamp()}")
            info_para.alignment = 1  # Center alignment

            # Add some space
            doc.add_paragraph()

            # Add the page image
            # Calculate image size to fit page width (with margins)
            page_width = Inches(7.5)  # Standard page width minus margins

            # Get image dimensions
            img = Image.open(temp_image_path)
            img_width, img_height = img.size
            aspect_ratio = img_height / img_width

            # Calculate display size
            display_width = page_width
            display_height = page_width * aspect_ratio

            # If height is too large, scale down
            max_height = Inches(9)  # Maximum height
            if display_height > max_height:
                display_height = max_height
                display_width = max_height / aspect_ratio

            # Add image to document
            paragraph = doc.add_paragraph()
            paragraph.alignment = 1  # Center alignment
            run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
            run.add_picture(temp_image_path, width=display_width, height=display_height)

            # Save the document
            doc.save(output_path)

            # Clean up temporary file
            try:
                os.unlink(temp_image_path)
            except:
                pass

            self.lbl_status.configure(text=f"Page exported successfully to {os.path.basename(output_path)}")

            # Show success message
            result = messagebox.askyesno(
                "Export Complete",
                f"Page {self.current_page + 1} has been exported to:\n{output_path}\n\nWould you like to open the Word document?"
            )

            if result:
                try:
                    os.startfile(output_path)  # Windows
                except:
                    try:
                        subprocess.run(['open', output_path])  # macOS
                    except:
                        try:
                            subprocess.run(['xdg-open', output_path])  # Linux
                        except:
                            pass

            logging.info(f"Page {self.current_page + 1} exported to {output_path}")

        except Exception as e:
            error_msg = f"Error exporting to Word: {str(e)}"
            self.lbl_status.configure(text=error_msg)
            messagebox.showerror("Export Error", error_msg)
            logging.error(f"Error exporting to Word: {e}")

    def get_current_timestamp(self):
        """Get current timestamp as formatted string."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def on_closing(self):
        """Handle application closing."""
        if self.pdf_document:
            self.pdf_document.close()
        self.destroy()


def main():
    """Main entry point for the application."""
    try:
        # Set up logging
        log_file = setup_logging("pdf_page_to_word")
        logging.info("Starting PDF Page to Word Converter")

        # Create and run the application
        app = PDFPageToWordApp()
        app.protocol("WM_DELETE_WINDOW", app.on_closing)
        app.mainloop()

        logging.info("Application closed")
    except Exception as e:
        logging.error(f"Unhandled exception: {e}")
        print(f"An error occurred: {e}")
        print(f"See log file for details: {log_file}")
        sys.exit(1)


if __name__ == "__main__":
    main()
