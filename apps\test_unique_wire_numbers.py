#!/usr/bin/env python3
"""
Test script for unique wire number generation logic

This script tests the generate_unique_wire_number method to ensure
it correctly handles duplicate wire numbers by appending letter suffixes.

Author: Assistant
Date: 2025-01-01
"""

import sys
import os

# Add the apps directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from set_wire_numbers import WireNumber<PERSON>signer

def test_unique_wire_number_generation():
    """Test the unique wire number generation logic"""
    print("Testing unique wire number generation...")
    
    # Create an instance of the wire number assigner
    assigner = WireNumberAssigner()
    
    # Test case 1: No conflicts
    used_numbers = set()
    result = assigner.generate_unique_wire_number("1A5", used_numbers)
    print(f"Test 1 - No conflict: '1A5' -> '{result}'")
    assert result == "1A5", f"Expected '1A5', got '{result}'"

    # Test case 2: One conflict
    used_numbers = {"1A5"}
    result = assigner.generate_unique_wire_number("1A5", used_numbers)
    print(f"Test 2 - One conflict: '1A5' -> '{result}'")
    assert result == "1A5.A", f"Expected '1A5.A', got '{result}'"

    # Test case 3: Multiple conflicts
    used_numbers = {"1A5", "1A5.A", "1A5.B"}
    result = assigner.generate_unique_wire_number("1A5", used_numbers)
    print(f"Test 3 - Multiple conflicts: '1A5' -> '{result}'")
    assert result == "1A5.C", f"Expected '1A5.C', got '{result}'"

    # Test case 4: Many conflicts (test alphabet progression)
    used_numbers = {f"2B3.{chr(ord('A') + i)}" for i in range(5)}  # A through E
    used_numbers.add("2B3")
    result = assigner.generate_unique_wire_number("2B3", used_numbers)
    print(f"Test 4 - Many conflicts: '2B3' -> '{result}'")
    assert result == "2B3.F", f"Expected '2B3.F', got '{result}'"

    # Test case 5: Different base numbers don't conflict
    used_numbers = {"1A5", "1A5.A"}
    result = assigner.generate_unique_wire_number("1B6", used_numbers)
    print(f"Test 5 - Different base: '1B6' -> '{result}'")
    assert result == "1B6", f"Expected '1B6', got '{result}'"
    
    print("All tests passed!")

def test_wire_number_calculation():
    """Test the wire number calculation logic"""
    print("\nTesting wire number calculation...")
    
    assigner = WireNumberAssigner()
    
    # Test normal cases
    result = assigner.calculate_wire_number("1", "A5")
    print(f"Test 1 - Normal: page='1', grid='A5' -> '{result}'")
    assert result == "1A5", f"Expected '1A5', got '{result}'"

    result = assigner.calculate_wire_number("10", "B12")
    print(f"Test 2 - Normal: page='10', grid='B12' -> '{result}'")
    assert result == "10B12", f"Expected '10B12', got '{result}'"

    # Test edge cases
    result = assigner.calculate_wire_number("", "C7")
    print(f"Test 3 - Empty page: page='', grid='C7' -> '{result}'")
    assert result == "0C7", f"Expected '0C7', got '{result}'"

    result = assigner.calculate_wire_number(None, "D8")
    print(f"Test 4 - None page: page=None, grid='D8' -> '{result}'")
    assert result == "0D8", f"Expected '0D8', got '{result}'"
    
    print("Wire number calculation tests passed!")

def main():
    """Main test function"""
    print("=" * 50)
    print("Testing Wire Number Assignment Logic")
    print("=" * 50)
    
    try:
        test_unique_wire_number_generation()
        test_wire_number_calculation()
        
        print("\n" + "=" * 50)
        print("ALL TESTS PASSED SUCCESSFULLY!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\nTEST FAILED: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
