import customtkinter as ctk
from tkinter import filedialog, messagebox
import os
import sys
import win32com.client
import logging

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import theme utilities
try:
    from lib.theme_utils import apply_theme
except ImportError:
    # Define a basic theme utility if import fails
    def apply_theme(theme_name="red", appearance_mode="dark"):
        ctk.set_appearance_mode(appearance_mode)
        ctk.set_default_color_theme("blue")

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Apply the red theme
apply_theme("red", "dark")

"""
DXF Email Sender Application

A GUI application for automatically sending DXF (Drawing Exchange Format) files via email
using Microsoft Outlook. This tool provides a simple interface for selecting a directory
containing DXF files and sending them as email attachments.

Features:
    - Directory browser for selecting folders containing DXF files
    - Automatic detection and listing of all DXF files in selected directory
    - Customizable recipient email address (<NAME_EMAIL>)
    - Integration with Microsoft Outlook for email sending
    - Modern dark-themed GUI using CustomTkinter
    - Error handling and logging for troubleshooting

Requirements:
    - Microsoft Outlook must be installed and configured
    - Python packages: customtkinter, pywin32
    - Windows operating system (due to win32com dependency)

Usage:
    1. Run the application
    2. Click "Select Folder" to choose a directory containing DXF files
    3. Verify or modify the recipient email address
    4. Review the list of DXF files that will be sent
    5. Click "Send DXF Files" to create and send the email

The application automatically creates an email with:
    - Subject: "DXF Files from [folder_name]"
    - Body: Description of attached files and source directory
    - Attachments: All DXF files found in the selected directory

Author: Work Scripts Collection
Version: 1.0
"""

class DXFEmailerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("DXF Email Sender")
        self.root.geometry("500x300")

        # Directory selection
        self.dir_frame = ctk.CTkFrame(self.root)
        self.dir_frame.pack(fill="x", padx=20, pady=10)

        self.btn_select = ctk.CTkButton(
            self.dir_frame,
            text="Select Folder",
            command=self.select_directory,
            width=120
        )
        self.btn_select.pack(side="left", padx=5)

        self.lbl_directory = ctk.CTkLabel(
            self.dir_frame,
            text="No folder selected",
            anchor="w"
        )
        self.lbl_directory.pack(side="left", fill="x", expand=True, padx=5)

        # Email input
        self.email_frame = ctk.CTkFrame(self.root)
        self.email_frame.pack(fill="x", padx=20, pady=10)

        self.lbl_email = ctk.CTkLabel(
            self.email_frame,
            text="Send to:",
            anchor="w"
        )
        self.lbl_email.pack(side="left", padx=5)

        self.email_var = ctk.StringVar(value="<EMAIL>")
        self.entry_email = ctk.CTkEntry(
            self.email_frame,
            textvariable=self.email_var,
            width=300
        )
        self.entry_email.pack(side="left", fill="x", expand=True, padx=5)

        # File list
        self.file_frame = ctk.CTkFrame(self.root)
        self.file_frame.pack(fill="both", expand=True, padx=20, pady=10)

        self.file_list = ctk.CTkTextbox(
            self.file_frame,
            height=150
        )
        self.file_list.pack(fill="both", expand=True)

        # Send button
        self.btn_send = ctk.CTkButton(
            self.root,
            text="Send DXF Files",
            command=self.send_files,
            height=40,
            font=("Arial", 14, "bold")
        )
        self.btn_send.pack(pady=20)

        self.current_directory = None

        # Initialize Outlook
        try:
            self.outlook = win32com.client.Dispatch('Outlook.Application')
        except Exception as e:
            messagebox.showerror("Error", "Failed to connect to Outlook. Please make sure Outlook is installed and running.")
            logging.error(f"Outlook initialization error: {str(e)}")
            root.destroy()

    def select_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.current_directory = directory
            self.lbl_directory.configure(text=directory)
            self.update_file_list()

    def update_file_list(self):
        self.file_list.delete("1.0", "end")
        if self.current_directory:
            dxf_files = [f for f in os.listdir(self.current_directory)
                        if f.lower().endswith('.dxf')]
            if dxf_files:
                self.file_list.insert("1.0", "\n".join(dxf_files))
            else:
                self.file_list.insert("1.0", "No DXF files found in selected directory")

    def send_files(self):
        if not self.current_directory:
            messagebox.showerror("Error", "Please select a directory first")
            return

        dxf_files = [f for f in os.listdir(self.current_directory)
                    if f.lower().endswith('.dxf')]

        if not dxf_files:
            messagebox.showerror("Error", "No DXF files found in selected directory")
            return

        try:
            # Create Outlook mail item
            mail = self.outlook.CreateItem(0)  # 0 = olMailItem
            mail.To = self.email_var.get()
            mail.Subject = f"DXF Files from {os.path.basename(self.current_directory)}"
            mail.Body = f"Attached are {len(dxf_files)} Data Plate DXF files from {self.current_directory}"

            # Attach files
            for filename in dxf_files:
                filepath = os.path.join(self.current_directory, filename)
                mail.Attachments.Add(filepath)

            # Send the email
            mail.Send()
            messagebox.showinfo("Success", "DXF files sent successfully via Outlook!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to send email: {str(e)}")
            logging.error(f"Email send error: {str(e)}")

def main():
    root = ctk.CTk()
    app = DXFEmailerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
