# PDF Section Printing Application

The PDF Section Printing Application automates the printing of technical documents with varying page sizes and formatting requirements. It detects page types based on size and applies appropriate printer settings for each section, eliminating the need for manual printer configuration between different document sections.

## Features

- **Automatic Page Type Detection**: Identifies Letter, Tabloid, and other page sizes
- **Section Grouping**: Groups consecutive pages of the same type into sections
- **Intelligent Printing**: Applies appropriate printer settings for each section type:
  - DOTX/ASME text: Letter size, portrait orientation, duplex, color
  - Drawings: Tabloid size, landscape orientation, single-sided, color
- **Multiple Printing Methods**: Supports various printing methods for maximum compatibility
- **Printer Profile Management**: Save and load printer profiles for different document types
- **PDF Preview**: View PDF files with page type information
- **User-Friendly Interface**: Clean, modern interface using customtkinter

## Installation

1. Ensure you have Python 3.6+ installed
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Optional: Install additional software for enhanced printing capabilities:
   - SumatraPDF
   - Adobe Reader
   - GhostScript

## Usage

1. Launch the application:
   ```
   python apps/print_merged_pdf_sections.py
   ```
2. Select a starting directory using the "Select Directory" button
3. Click "Search for MERGED Folders" to find PDF files in MERGED folders
4. Select a PDF file from the list to preview
5. Configure printer profiles if needed
6. Click "Print PDF Sections" to print the PDF with appropriate settings for each section

## Printer Profiles

The application includes default printer profiles for different page types:

- **Letter Profile**: Letter size, portrait orientation, duplex, color
- **Tabloid Profile**: Tabloid size, landscape orientation, single-sided, color
- **Other Profile**: Letter size, portrait orientation, single-sided, color

You can customize these profiles by selecting the appropriate tab in the Printer Profiles section and adjusting the settings.

## Printing Methods

The application attempts to print using the following methods, in order of preference:

1. Windows API printing
2. SumatraPDF command-line printing
3. Adobe Reader command-line printing
4. GhostScript direct printing
5. Default PDF viewer printing

If one method fails, the application automatically tries the next method.

## Troubleshooting

- **PDF files not found**: Make sure the PDF files are in folders named "MERGED" (case-insensitive)
- **Printing fails**: Check that the selected printer is available and properly configured
- **Page type detection issues**: The application uses standard page dimensions with a small tolerance; unusual page sizes may be classified as "Other"

## Development

The application is structured as follows:

- `print_merged_pdf_sections.py`: Main application file
- `lib/pdf_section_processor.py`: Functions for processing PDF files
- `lib/pdf_printing_engine.py`: Functions for printing PDF files
- `lib/pdf_printing_config.py`: Functions for managing configuration
- `lib/pdf_printing_logging.py`: Functions for logging and error handling
- `lib/pdf_printing_ui.py`: User interface components

Unit tests are located in the `test` directory and can be run using:
```
python -m unittest discover -s test
```

## License

PROPRIETARY: This software is proprietary and not open-source. For usage and distribution terms, please contact the author.
