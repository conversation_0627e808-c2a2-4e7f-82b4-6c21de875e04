# E3 Wire Number Assignment Tool

This tool automatically assigns wire numbers to all net segments in an E3.series project based on page number and grid/ladder position.

## Overview

The wire numbering tool reads through the current open E3 project and for every connection, calculates wire numbers and sets the "Wire number" attribute on the corresponding net segments based on:

- **Page number**: Retrieved from the sheet assignment
- **Grid/ladder position**: Retrieved from the pin's schema location

The wire number format is: `{page_number}({grid_position})`

### Key Features

- **Lowest wire number selection**: For connections with multiple ends, the end that results in the lowest wire number is used
- **Signal-based grouping**: All net segments with the same signal name get the same wire number
- **Automatic attribute detection**: Tries multiple common wire number attribute names
- **Comprehensive logging**: Detailed logging of all operations and errors

## Files

- `set_wire_numbers.py` - Main script that performs the wire number assignment
- `test_wire_numbers.py` - Test script to verify E3 connection and display project information
- `README_wire_numbering.md` - This documentation file

## Prerequisites

1. **E3.series** must be installed and running
2. **Python** with the following packages:
   - `win32com.client` (pywin32)
   - `logging` (built-in)
   - `sys` (built-in)
   - `collections` (built-in)
   - `re` (built-in)

3. **E3 Project** must be open in E3.series

## Installation

1. Ensure E3.series is installed and running
2. Install Python dependencies:
   ```bash
   pip install pywin32
   ```
3. Copy the script files to your desired location

## Usage

### Step 1: Test Connection (Recommended)

Before running the main script, test your E3 connection:

```bash
python test_wire_numbers.py
```

This will:
- Verify connection to E3
- Display project information
- Show sample connection and pin data
- Test attribute access capabilities

### Step 2: Run Wire Number Assignment

Once the test is successful, run the main script:

```bash
python set_wire_numbers.py
```

The script will:
1. Connect to the open E3 project
2. Analyze all connections and their pin locations
3. Calculate wire numbers based on page and grid position
4. Get net segments for each connection
5. Group net segments by signal name
6. Assign the lowest wire number to each signal group
7. Update all net segments with their calculated wire numbers

## Wire Number Calculation

### Format
```
{page_number}({grid_position})
```

### Examples
- Page 1, Grid A5: `1(A5)`
- Page 10, Grid B12: `10(B12)`
- Page 2, Column C, Row 7: `2(C7)`

### Grid Position Extraction

The script attempts to extract grid position in this order:
1. From grid description (format: `/sheet.grid`)
2. From column and row values combined
3. From column value only
4. From row value only
5. Fallback to "UNKNOWN"

### Lowest Wire Number Selection

For connections spanning multiple pins/locations, the script:
1. Calculates wire numbers for all pin locations
2. Sorts them lexicographically
3. Selects the lowest value
4. Applies this value to all connections in the same signal

## Attribute Names

The script attempts to set wire numbers using these attribute names (in order):
1. `Wire number`
2. `WIRE_NUMBER`
3. `WireNumber`
4. `Wire_Number`

The first successful attribute name is used for all connections.

## Logging

The script creates a detailed log file: `wire_numbering.log`

Log levels:
- **INFO**: General progress and successful operations
- **WARNING**: Non-critical issues (e.g., pins without schema location)
- **ERROR**: Critical errors that prevent processing
- **DEBUG**: Detailed debugging information (when enabled)

## Error Handling

Common issues and solutions:

### "Failed to connect to E3"
- Ensure E3.series is running
- Verify a project is open in E3
- Check that no other applications are blocking COM access

### "No connections found in project"
- Verify the project contains electrical connections
- Check that the project is properly loaded

### "Could not set wire number for connection"
- The project may use different attribute names for wire numbers
- Check the E3 project's attribute definitions
- Manually verify attribute names in E3

### "Pin has no schema location"
- Some pins may not be placed on schematic sheets
- This is normal for certain types of connections
- These connections will be skipped

## Customization

### Modifying Wire Number Format

To change the wire number format, edit the `calculate_wire_number` method in `set_wire_numbers.py`:

```python
def calculate_wire_number(self, page_number, grid_position):
    # Current format: page_number(grid_position)
    wire_number = f"{page_num}({grid_position})"
    
    # Example alternative formats:
    # wire_number = f"{page_num}-{grid_position}"  # Dash separator
    # wire_number = f"W{page_num}{grid_position}"  # With prefix
    
    return wire_number
```

### Adding Custom Attribute Names

To add custom wire number attribute names, edit the attribute list in the `process_connections` method:

```python
for attr_name in ["Wire number", "WIRE_NUMBER", "WireNumber", "Wire_Number", "YOUR_CUSTOM_NAME"]:
```

## Technical Details

### DTM API Methods Used

- `e3Job.GetAllConnectionIds()` - Get all connection IDs
- `e3Connection.SetId()` - Set active connection
- `e3Connection.GetSignalName()` - Get signal name
- `e3Connection.GetPinIds()` - Get connection pin IDs
- `e3Connection.GetNetSegmentIds()` - Get net segment IDs for connection
- `e3NetSegment.SetId()` - Set active net segment
- `e3NetSegment.SetAttributeValue()` - Set wire number attribute on net segment
- `e3Pin.SetId()` - Set active pin
- `e3Pin.GetSchemaLocation()` - Get pin location information
- `e3Sheet.SetId()` - Set active sheet
- `e3Sheet.GetAssignment()` - Get sheet page number

### COM Object Management

The script properly manages COM objects by:
- Creating objects through the E3 application
- Setting objects to None for cleanup
- Using try/except blocks for error handling

## Version Information

- **Script Version**: 1.0
- **E3.series Compatibility**: v2023-24.30 (and likely earlier versions)
- **Python Version**: 3.6+

## Support

For issues or questions:
1. Check the log file for detailed error information
2. Verify E3 project structure and attribute definitions
3. Test with the provided test script first
4. Ensure all prerequisites are met

## License

This script is provided as-is for educational and automation purposes. Use at your own risk and always backup your E3 projects before running automated scripts.
