# PDF Page to Word Converter

A user-friendly GUI application that allows you to select a PDF file, browse through its pages with a preview, select a specific page, and export that page to a Word document.

## Features

- **PDF File Selection**: Easy file browser to select any PDF document
- **Page Navigation**: Browse through PDF pages with intuitive navigation controls
  - Previous/Next buttons
  - Direct page number entry with "Go to Page" functionality
  - Page counter display (e.g., "Page 3 of 15")
- **High-Quality Preview**: Real-time preview of the current page with zoom and scroll capabilities
- **Page Export**: Export any selected page to a Word document with:
  - High-quality image rendering (3x zoom for crisp output)
  - Professional document formatting
  - Source information (original PDF name, page number, export timestamp)
  - Automatic image sizing to fit Word page dimensions
- **Modern GUI**: Clean, modern interface using CustomTkinter with dark theme

## Requirements

The application requires the following Python packages (automatically installed via requirements.txt):

- `customtkinter==5.2.1` - Modern GUI framework
- `PyMuPDF==1.23.14` - PDF processing and rendering
- `python-docx==1.0.1` - Word document creation
- `Pillow==10.1.0` - Image processing
- `tkinter` - GUI components (included with Python)

## Installation

1. Ensure you have Python 3.8+ installed
2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Running the Application

1. **From the Engineering Tools Launcher**:
   - Run `python apps_launcher.py`
   - Find "PDF Page to Word Converter" in the apps list
   - Click to launch

2. **Direct execution**:
   ```bash
   python apps/pdf_page_to_word.py
   ```

### Using the Application

1. **Select PDF File**:
   - Click "Select PDF File" button
   - Browse and select your PDF document
   - The application will load and display the first page

2. **Navigate Pages**:
   - Use "◀ Previous" and "Next ▶" buttons to browse pages
   - Or enter a specific page number and click "Go"
   - The preview will update automatically

3. **Export to Word**:
   - Navigate to the page you want to export
   - Click "Export Current Page to Word"
   - Choose where to save the Word document
   - The application will create a professional Word document with the page image

### Output Format

The exported Word document includes:
- **Title**: "PDF Page Export"
- **Source Information**: 
  - Original PDF filename
  - Page number (e.g., "Page 5 of 20")
  - Export timestamp
- **High-Quality Page Image**: Automatically sized to fit the Word page

## Technical Details

### PDF Processing
- Uses PyMuPDF (fitz) for high-performance PDF rendering
- Renders pages at 3x resolution for crisp Word document output
- Supports all standard PDF formats and features

### Image Quality
- Preview: 2x zoom for smooth navigation
- Export: 3x zoom for high-quality Word output
- Automatic aspect ratio preservation
- Smart sizing to fit Word page dimensions

### Word Document Creation
- Professional formatting with centered alignment
- Automatic image scaling (max 7.5" width, 9" height)
- Metadata inclusion for document tracking
- Standard .docx format compatible with all Word versions

## File Structure

```
apps/
├── pdf_page_to_word.py          # Main application
└── README_PDF_Page_to_Word.md   # This documentation
```

## Error Handling

The application includes comprehensive error handling for:
- Invalid PDF files
- Corrupted or password-protected PDFs
- File system permissions
- Memory limitations with large PDFs
- Export failures

All errors are logged and displayed to the user with helpful messages.

## Logging

The application creates detailed logs in the `logs/` directory:
- `pdf_page_to_word.log` - Application events and errors
- Automatic log rotation and cleanup

## Compatibility

- **Operating System**: Windows, macOS, Linux
- **Python Version**: 3.8+
- **PDF Support**: All standard PDF formats
- **Word Output**: .docx format (Word 2007+)

## Performance

- **Memory Efficient**: Only loads current page into memory
- **Fast Rendering**: Optimized preview generation
- **Large File Support**: Handles PDFs with hundreds of pages
- **Responsive UI**: Non-blocking operations with progress feedback

## Use Cases

- **Document Review**: Extract specific pages for review or sharing
- **Report Creation**: Include PDF pages in Word reports
- **Presentation Prep**: Convert technical drawings or charts for presentations
- **Archive Management**: Create Word versions of important PDF pages
- **Quality Control**: Extract pages for detailed analysis or markup

## License

PROPRIETARY: This software is proprietary and not open-source. For usage and distribution terms, please contact the author.
